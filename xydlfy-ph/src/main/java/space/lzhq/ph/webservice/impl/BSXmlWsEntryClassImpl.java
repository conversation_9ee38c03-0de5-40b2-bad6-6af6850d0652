package space.lzhq.ph.webservice.impl;

import com.ruoyi.common.utils.spring.SpringUtils;
import jakarta.annotation.PreDestroy;
import jakarta.jws.WebService;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dromara.hutool.core.data.id.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.OutpatientStopClinic;
import space.lzhq.ph.service.OutpatientStopClinicService;
import space.lzhq.ph.webservice.BSXmlWsEntryClass;

import java.util.Arrays;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

@Service
@WebService(
        serviceName = "BSXmlWsEntryClassService",
        portName = "BSXmlWsEntryClassPort",
        targetNamespace = "http://ws.access.hai/",
        endpointInterface = "space.lzhq.ph.webservice.BSXmlWsEntryClass"
)
public class BSXmlWsEntryClassImpl implements BSXmlWsEntryClass {

    private static final Logger log = LoggerFactory.getLogger(BSXmlWsEntryClassImpl.class);

    private final OutpatientStopClinicService outpatientStopClinicService;
    private final ExecutorService subscribeMessageExecutorService;


    @Autowired
    public BSXmlWsEntryClassImpl(OutpatientStopClinicService outpatientStopClinicService, @Qualifier("subscribeMessageExecutorService") ExecutorService subscribeMessageExecutorService) {
        this.outpatientStopClinicService = outpatientStopClinicService;
        this.subscribeMessageExecutorService = subscribeMessageExecutorService;
    }

    /**
     * 生成响应XML
     *
     * @param success 是否成功
     * @param detail  详细信息
     * @return XML字符串
     */
    private static String generateResponseXml(boolean success, String detail) {
        try {
            Document document = DocumentHelper.createDocument();
            Element root = document.addElement("BSXml");

            Element header = root.addElement("MsgHeader");
            header.addElement("Sender").setText("YQTL");
            header.addElement("MsgType").setText("OutAppointSend");
            header.addElement("MsgVersion").setText("3.1");
            header.addElement("Status").setText(String.valueOf(success));
            header.addElement("Detail").setText(detail);

            return document.asXML();
        } catch (Exception e) {
            log.error("生成响应XML时发生错误", e);
            return String.format("<BSXml><MsgHeader><Status>false</Status><Detail>生成响应失败: %s</Detail></MsgHeader></BSXml>",
                    e.getMessage());
        }
    }

    @PreDestroy
    public void destroy() {
        if (subscribeMessageExecutorService != null) {
            subscribeMessageExecutorService.shutdown();
            try {
                if (!subscribeMessageExecutorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    subscribeMessageExecutorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                subscribeMessageExecutorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    @Override
    public String invoke(String service, String urid, String pwd, String parameter) {
        String traceId = IdUtil.simpleUUID();
        log.info("SOAP#{} service: {}, urid: {}, parameter: {}", traceId, service, urid, parameter);

        final String expectedUserId = "YQTL";
        final String expectedPassword = "ysrNyK62ton7b4PN";
        if (!expectedUserId.equals(urid) || !expectedPassword.equals(pwd)) {
            log.info("SOAP#{} service: {}, urid: {} 认证失败", traceId, service, urid);
            return generateResponseXml(false, "认证失败");
        }

        try {
            return switch (service) {
                case SERVICE_TYPE_OUT_APPOINT_SEND_STOP -> handleOutpatientStopClinic(traceId, parameter);
                case SERVICE_TYPE_INSPECTION_STATUS_UPDATE -> handleInspectionStatusUpdate(traceId, parameter);
                case SERVICE_TYPE_LAB_REPORT_AUDIT -> handleLabReportAudit(traceId, parameter);
                default -> {
                    log.info("SOAP#{} service: {}, 未知的服务类型", traceId, service);
                    yield generateResponseXml(false, "未知的服务类型: " + service);
                }
            };
        } catch (Exception e) {
            log.error("SOAP#{} service: {}, 发生错误: ", traceId, service, e);
            return generateResponseXml(false, "处理请求时发生错误: " + e.getMessage());
        }
    }

    private String handleLabReportAudit(String traceId, String parameter) {
        return generateResponseXml(false, "尚未实现");
    }

    private String handleInspectionStatusUpdate(String traceId, String parameter) {
        return generateResponseXml(false, "尚未实现");
    }

    private boolean isTestEnvironment() {
        return Arrays.asList(SpringUtils.getActiveProfiles()).contains("test");
    }

    private void sendSubscribeMessage(OutpatientStopClinic stopClinic, String traceId) {
        try {
            outpatientStopClinicService.sendSubscribeMessage(stopClinic);
        } catch (Exception e) {
            log.error("SOAP#{} 发送订阅消息失败: ", traceId, e);
        }
    }

    /**
     * 处理门诊停诊消息
     */
    private String handleOutpatientStopClinic(String traceId, String parameter) {
        try {
            OutpatientStopClinic stopClinic = OutpatientStopClinic.Parser.parseFromXml(parameter);
            if (outpatientStopClinicService.existsByAppointmentId(stopClinic.getAppointmentId())) {
                log.info("SOAP#{} 消息已存在", traceId);
                return generateResponseXml(true, "消息已存在");
            }

            boolean success = outpatientStopClinicService.save(stopClinic);
            if (success) {
                log.info("SOAP#{} 消息已保存", traceId);

                // 测试环境下同步发送订阅消息
                if (isTestEnvironment()) {
                    sendSubscribeMessage(stopClinic, traceId);
                } else {
                    // 生产环境下异步发送订阅消息
                    CompletableFuture.runAsync(() -> sendSubscribeMessage(stopClinic, traceId), subscribeMessageExecutorService);
                }
            } else {
                log.info("SOAP#{} 消息保存失败", traceId);
            }
            return generateResponseXml(success, success ? "成功" : "保存失败");
        } catch (Exception e) {
            log.error("SOAP#{} 发生错误: ", traceId, e);
            return generateResponseXml(false, "处理门诊停诊消息失败: " + e.getMessage());
        }
    }

}
